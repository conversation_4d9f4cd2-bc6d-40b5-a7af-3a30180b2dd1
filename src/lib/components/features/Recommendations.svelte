<script lang="ts">
    import ReadingActivityModal from '$lib/components/features/ReadingActivityModal.svelte';
    import Container from '$lib/components/layout/body/Container.svelte';
    import BookListUI from '$lib/components/ui/BookListUI.svelte';
    import type Book from '$lib/domain/Book';
    import type ReadingActivity from '$lib/domain/ReadingActivity';
    import type Recommendations from '$lib/domain/Recommendations';
    import {t} from '$lib/localization/Localization';
    import AppRoutes from '$routes/AppRoutes';

    export let recommendations: Recommendations;

    let showEditModal = false;
    let editingActivity: ReadingActivity | null = null;
    let editingBook: Book | null = null;

    function handleEditActivity(activity: ReadingActivity, book: Book) {
        editingActivity = activity;
        editingBook = book;
        showEditModal = true;
    }

    function handleActivitySaved() {
        showEditModal = false;
        editingActivity = null;
        editingBook = null;
    }
</script>

{#if recommendations.currentlyReading.length > 0}
    <Container title={$t('recommendations.continueReading')}>
        <BookListUI
            books={recommendations.currentlyReading}
            onEditActivity={handleEditActivity}
        />
        <ReadingActivityModal
            bind:open={showEditModal}
            book={editingBook}
            editingActivity={editingActivity}
            isEditing={true}
            modalTriggeredByStatusChange={false}
            onActivitySaved={handleActivitySaved}
        />
    </Container>
{/if}
{#if recommendations.top.length > 0}
    <h1 class="mb-3 mt-6 text-center text-2xl font-bold">{$t('recommendations.topThreeRecommendations')}</h1>
    <div class="container mx-auto flex flex-wrap justify-center">
        {#each recommendations.top as book, index (index)}
            <div class="inline-block p-2 text-center lg:w-1/3">
                <a href={AppRoutes.book(book.uuid)}>
                    <img src={book.cover} class="mx-auto h-64 w-full rounded-2xl object-contain" alt="Book cover"/>
                    <div>{book.title}</div>
                </a>
            </div>
        {/each}
    </div>
{/if}
{#if recommendations.likes.length > 0}
    <Container title={$t('recommendations.moreBooksLikes')}>
        <BookListUI books={recommendations.likes}/>
    </Container>
{/if}
{#if recommendations.likes_free.length > 0}
    <Container title={$t('recommendations.freeBooksLikes')}>
        <BookListUI books={recommendations.likes_free}/>
    </Container>
{/if}
{#if recommendations.authors.length > 0}
    <Container title={$t('recommendations.moreBooksAuthors')}>
        <BookListUI books={recommendations.authors}/>
    </Container>
{/if}
{#if recommendations.subjects.length > 0}
    <Container title={$t('recommendations.moreBooksSubjects')}>
        <BookListUI books={recommendations.subjects}/>
    </Container>
{/if}
