import type {BaseApiResponse} from '$lib/api/base/BaseApiClient';
import MainApiClient from '$lib/api/base/MainApiClient';
import type ReadingActivity from '$lib/domain/ReadingActivity';

export default class ReadingActivitiesApiClient extends MainApiClient {
    public async index(bookId: string): Promise<IndexResponse> {
        return await this.get(`/reading-activities/${bookId}`);
    }

    public async store(data: CreateReadingActivityData): Promise<StoreResponse> {
        return await this.post('/reading-activities', data);
    }

    public async update(activityId: string, data: UpdateReadingActivityData): Promise<UpdateResponse> {
        return await this.post(`/reading-activities/${activityId}`, data);
    }

    public async destroy(activityId: string): Promise<DestroyResponse> {
        return await this.delete(`/reading-activities/${activityId}`);
    }
}

interface IndexResponse extends BaseApiResponse {
    data: ReadingActivity[];
}

interface StoreResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}

interface UpdateResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}

interface DestroyResponse extends BaseApiResponse {
    data: {
        success: boolean;
    };
}

export interface CreateReadingActivityData {
    bookId: string;
    languageCode?: string | null;
    startDate?: string | null;
    endDate?: string | null;
    pagesRead?: number | null;
    totalPages?: number | null;
}

export interface UpdateReadingActivityData {
    languageCode?: string | null;
    startDate?: string | null;
    endDate?: string | null;
    pagesRead?: number | null;
    totalPages?: number | null;
}
