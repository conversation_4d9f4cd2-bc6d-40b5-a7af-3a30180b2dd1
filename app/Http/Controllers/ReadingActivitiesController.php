<?php declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Resources\ReadingActivityResource;
use App\Models\Book;
use App\Models\Language;
use App\Models\ReadingActivity;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

final class ReadingActivitiesController extends Controller
{
    public function __construct()
    {
        $this->middleware([
            'auth:sanctum',
            'verified',
        ]);
    }

    public function index(Book $book): array
    {
        $activities = ReadingActivity::where('book_id', $book->id)
            ->where('user_id', Auth::id())
            ->with(['language'])
            ->orderBy('created_at', 'desc')
            ->get();

        return ReadingActivityResource::collection($activities)->toArray(request());
    }

    public function store(Request $request): array
    {
        $validated = $request->validate([
            'bookId' => 'required|string|exists:books,uuid',
            'languageCode' => 'nullable|string|exists:languages,code',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'pagesRead' => 'nullable|integer|min:0',
            'totalPages' => 'nullable|integer|min:1',
        ]);

        if (isset($validated['pagesRead']) && isset($validated['totalPages'])
            && $validated['pagesRead'] > $validated['totalPages']) {
            throw ValidationException::withMessages([
                'pagesRead' => 'Pages read cannot be greater than total pages.',
            ]);
        }

        $book = Book::where('uuid', $validated['bookId'])->firstOrFail();
        $language = null;

        if (!empty($validated['languageCode'])) {
            $language = Language::where('code', $validated['languageCode'])->first();
        }

        $activity = new ReadingActivity();
        $activity->uuid = Str::uuid()->toString();
        $activity->book_id = $book->id;
        $activity->user_id = Auth::id();
        $activity->language_id = $language?->id;
        $activity->start_date = $validated['startDate'] ?? null;
        $activity->end_date = $validated['endDate'] ?? null;
        $activity->pages_read = $validated['pagesRead'] ?? null;
        $activity->total_pages = $validated['totalPages'] ?? null;

        $success = $activity->save();

        return [
            'success' => $success,
        ];
    }

    public function update(string $activityUuid, Request $request): array
    {
        $activity = ReadingActivity::where('uuid', $activityUuid)->firstOrFail();

        if ($activity->user_id !== Auth::id()) {
            abort(403, 'Unauthorized');
        }

        $validated = $request->validate([
            'languageCode' => 'nullable|string|exists:languages,code',
            'startDate' => 'nullable|date',
            'endDate' => 'nullable|date|after_or_equal:startDate',
            'pagesRead' => 'nullable|integer|min:0',
            'totalPages' => 'nullable|integer|min:1',
        ]);

        if (isset($validated['pagesRead']) && isset($validated['totalPages'])
            && $validated['pagesRead'] > $validated['totalPages']) {
            throw ValidationException::withMessages([
                'pagesRead' => 'Pages read cannot be greater than total pages.',
            ]);
        }

        $language = null;
        if (!empty($validated['languageCode'])) {
            $language = Language::where('code', $validated['languageCode'])->first();
        }

        if (array_key_exists('startDate', $validated)) {
            $activity->start_date = $validated['startDate'];
        }

        if (array_key_exists('endDate', $validated)) {
            $activity->end_date = $validated['endDate'];
        }

        if (array_key_exists('pagesRead', $validated)) {
            $activity->pages_read = $validated['pagesRead'];
        }

        if (array_key_exists('totalPages', $validated)) {
            $activity->total_pages = $validated['totalPages'];
        }

        $success = $activity->save();

        return [
            'success' => $success,
        ];
    }

    public function destroy(string $activityUuid): array
    {
        $activity = ReadingActivity::where('uuid', $activityUuid)->firstOrFail();

        if ($activity->user_id !== Auth::id()) {
            abort(403, 'Unauthorized');
        }

        $success = $activity->delete();

        return [
            'success' => $success,
        ];
    }
}
